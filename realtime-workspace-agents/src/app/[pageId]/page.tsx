import React, { Suspense } from "react";
import { TranscriptProvider } from "@/app/contexts/TranscriptContext";
import { EventProvider } from "@/app/contexts/EventContext";
import { WorkspaceProvider } from "@/app/contexts/WorkspaceContext";
import App from "../App";

interface PageProps {
  params: Promise<{
    pageId: string;
  }>;
}

export default async function Page({ params }: PageProps) {
  const { pageId } = await params;

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TranscriptProvider>
        <EventProvider>
          <WorkspaceProvider>
            <App pageId={pageId} />
          </WorkspaceProvider>
        </EventProvider>
      </TranscriptProvider>
    </Suspense>
  );
}
